### 关联 Issue，本次PR 关联的所有 task 或 story 都填进来，PR 合并后这些 task 会自动关闭
- close olaola-chat/DataWarehouse#3352
- close olaola-chat/DataWarehouse#3353

###  本次PR简介，如果 2 个Issue修改的是同一个表，下方的信息可以合并，指标和应用场景为选填
#3352 
    修改/新增表:db_name.table_name  
    背景: XX需求
    逻辑: 修改或新增 XX
    指标: 指标 1
          指标 2
    应用场景: 计算 XX
#3353
    修改/新增表:db_name.table_name  
    背景: XX需求
    逻辑: 修改或新增 XX
    指标: 指标 1
          指标 2
    应用场景: 计算 XX
