name: Check if PR is linked to an issue
on:
  pull_request:
    types:
      - edited
      - opened
      - reopened
      - synchronize

jobs:
  check_issue_linking:
    runs-on: self-hosted
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.PR_CHECK_APP_ID }}
          private-key: ${{ secrets.PR_CHECK_PRIVATE_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Check if PR is linked to an issue
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |
          checkLinkage=(
            graphql
            -f url=${{ github.event.pull_request.html_url }}
            -f query='query PullRequest($url: URI!) {
                resource(url: $url) {
                  ... on PullRequest {
                    title
                    closingIssuesReferences {
                      totalCount
                    }
                  }
                }
              }'
            --jq '.data.resource.closingIssuesReferences.totalCount > 0' 
          )

          linked=$(gh api "${checkLinkage[@]}")

          if [[ $linked != true ]]; then
            echo "::error title=PR is not linked to an issue.::Check the guide https://docs.github.com/en/issues/tracking-your-work-with-issues/linking-a-pull-request-to-an-issue"
            exit 1
          fi