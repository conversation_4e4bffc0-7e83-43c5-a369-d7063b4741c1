CREATE TABLE `paimon`.`dws_pa_rt`.`tms_trans_consume_user_acep_rf` (
  `uid` BIGINT NOT NULL COMMENT '用户uid',
  `acep_uid` BIGINT NOT NULL COMMENT '被打赏uid',
  `g_1` BIGINT COMMENT '分组1',
  `consume_amt_da_001` BIGINT COMMENT '历史累计消费金额，币分',
  `g_2` BIGINT COMMENT '分组2',
  `consume_cnt_da_001` BIGINT COMMENT '历史累计消费次数',
  `proc_time` AS PROCTIME(),
  CONSTRAINT `PK_uid_acep_uid` PRIMARY KEY (`uid`, `acep_uid`) NOT ENFORCED
) COMMENT 'PA-用户对用户消费历史累计表，粒度：uid+acep_uid'
WITH (
  'num-sorted-run.stop-trigger' = '2147483647',
  'fields.g_1.sequence-group' = 'consume_amt_da_001',
  'fields.consume_amt_da_001.aggregate-function' = 'sum',
  'fields.g_2.sequence-group' = 'consume_cnt_da_001',
  'fields.consume_cnt_da_001.aggregate-function' = 'sum',
  'scan.infer-parallelism' = 'false',
  'tag.automatic-creation' = 'process-time',
  'changelog-producer' = 'lookup',
  'tag.creation-period' = 'daily',
  'sort-spill-threshold' = '10',
  'bucket' = '8',
  'file.compression' = 'snappy',
  'path' = 'oss://sg-emr-data/dw/dws_pa_rt/tms_trans_consume_user_acep_rf',
  'changelog-producer.lookup-wait' = 'false',
  'merge-engine' = 'partial-update',
  'snapshot.time-retained' = '7d',
  'file.format' = 'orc',
  'tag.num-retained-max' = '2000',
  'metastore.tag-to-partition' = 'dt'
)
------------------------------------------------------ck建表------------------------------------------------------------
CREATE TABLE dws_pa_rt.tms_trans_consume_en_user_acep_rf_rep
    (
        `uid` Int64 COMMENT '打赏uid',
        `acep_uid` Int64 COMMENT '被打赏uid',
        `consume_amt_da_001` Int64 COMMENT '历史累计消费金额，币分',
        `consume_cnt_da_001` Int64 COMMENT '历史累计消费次数'
    )
    ENGINE = ReplacingMergeTree
    ORDER BY (uid, acep_uid)
;