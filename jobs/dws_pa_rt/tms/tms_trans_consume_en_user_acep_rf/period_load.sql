
-- 用户对主播消费累积表计算
insert into paimon.dws_pa_rt.tms_trans_consume_user_acep_rf
select
    src_uid uid,
    dst_uid acep_uid,
    0,
    money,
    0,
    1
from paimon.dws_pa_rt.dwd_trans_consume_journal_ri /*+ OPTIONS('scan.timestamp-millis' = '1736694000000') */
where dt >= '2025-01-13'
  and src_uid > 0
  and dst_uid > 0
;

-- 将英文区的用户消费数据写ck
CREATE CATALOG ck WITH (
    'type' = 'clickhouse',
    'url' = 'clickhouse://cc-gs5r0rk69n550843t.clickhouse.ads.aliyuncs.com:8123',
    'username' = 'bi',
    'password' = 'RboUi8E1gRmm',
    'database-name' = 'dws_pa_rt',
    'use-local' = 'false',
    'sink.batch-size' = '300',
    'sink.flush-interval' = '1000',
    'sink.update-strategy' = 'insert',
    'sink.ignore-delete' = 'true',
    'catalog.ignore-primary-key' = 'false'
);


insert into ck.dws_pa_rt.tms_trans_consume_en_user_acep_rf_dis
select
    a.uid,
    a.acep_uid,
    a.consume_amt_da_001,
    a.consume_cnt_da_001
from paimon.dws_pa_rt.tms_trans_consume_user_acep_rf a
         join paimon.dws_pa_rt.dim_user_rf FOR SYSTEM_TIME AS OF a.proc_time b
on a.uid = b.uid and b.bigarea_name = '英语区'
;